// SuperClaude Behavioral Modes
export interface BehavioralMode {
  id: string;
  name: string;
  description: string;
  icon: string;
  settings: ModeSettings;
  activate: () => void;
  deactivate: () => void;
}

export interface ModeSettings {
  verbosity: 'minimal' | 'normal' | 'detailed';
  creativity: number; // 0-100
  analysisDepth: 'shallow' | 'moderate' | 'deep';
  responseStyle: 'concise' | 'balanced' | 'comprehensive';
  focusAreas: string[];
}

export class BehavioralModeManager {
  private currentMode: BehavioralMode | null = null;
  private modes: Map<string, BehavioralMode> = new Map();

  constructor() {
    this.initializeModes();
  }

  private initializeModes() {
    // Brainstorming Mode
    this.registerMode({
      id: 'brainstorming',
      name: 'Brainstorming Mode',
      description: 'Creative ideation with divergent thinking and exploration',
      icon: '💡',
      settings: {
        verbosity: 'detailed',
        creativity: 90,
        analysisDepth: 'moderate',
        responseStyle: 'comprehensive',
        focusAreas: ['ideation', 'alternatives', 'possibilities', 'what-if scenarios']
      },
      activate: () => {
        console.log('Activating Brainstorming Mode');
        // Set AI parameters for creative thinking
      },
      deactivate: () => {
        console.log('Deactivating Brainstorming Mode');
      }
    });

    // Orchestration Mode
    this.registerMode({
      id: 'orchestration',
      name: 'Orchestration Mode',
      description: 'Multi-agent coordination for complex tasks',
      icon: '🎭',
      settings: {
        verbosity: 'normal',
        creativity: 50,
        analysisDepth: 'deep',
        responseStyle: 'balanced',
        focusAreas: ['agent-coordination', 'task-delegation', 'parallel-processing']
      },
      activate: () => {
        console.log('Activating Orchestration Mode');
        // Enable multi-agent features
      },
      deactivate: () => {
        console.log('Deactivating Orchestration Mode');
      }
    });

    // Token Efficiency Mode
    this.registerMode({
      id: 'token-efficiency',
      name: 'Token Efficiency Mode',
      description: 'Optimized for minimal token usage while maintaining quality',
      icon: '⚡',
      settings: {
        verbosity: 'minimal',
        creativity: 30,
        analysisDepth: 'shallow',
        responseStyle: 'concise',
        focusAreas: ['efficiency', 'directness', 'essential-information']
      },
      activate: () => {
        console.log('Activating Token Efficiency Mode');
        // Configure for minimal token usage
      },
      deactivate: () => {
        console.log('Deactivating Token Efficiency Mode');
      }
    });

    // Task Management Mode
    this.registerMode({
      id: 'task-management',
      name: 'Task Management Mode',
      description: 'Structured approach to breaking down and tracking tasks',
      icon: '📋',
      settings: {
        verbosity: 'normal',
        creativity: 40,
        analysisDepth: 'moderate',
        responseStyle: 'balanced',
        focusAreas: ['planning', 'prioritization', 'tracking', 'dependencies']
      },
      activate: () => {
        console.log('Activating Task Management Mode');
        // Enable task tracking features
      },
      deactivate: () => {
        console.log('Deactivating Task Management Mode');
      }
    });

    // Introspection Mode
    this.registerMode({
      id: 'introspection',
      name: 'Introspection Mode',
      description: 'Deep self-analysis and reasoning transparency',
      icon: '🔍',
      settings: {
        verbosity: 'detailed',
        creativity: 60,
        analysisDepth: 'deep',
        responseStyle: 'comprehensive',
        focusAreas: ['reasoning', 'self-analysis', 'thought-process', 'assumptions']
      },
      activate: () => {
        console.log('Activating Introspection Mode');
        // Enable detailed reasoning output
      },
      deactivate: () => {
        console.log('Deactivating Introspection Mode');
      }
    });
  }

  private registerMode(mode: BehavioralMode) {
    this.modes.set(mode.id, mode);
  }

  activateMode(modeId: string): boolean {
    const mode = this.modes.get(modeId);
    if (!mode) {
      console.error(`Mode ${modeId} not found`);
      return false;
    }

    // Deactivate current mode if exists
    if (this.currentMode) {
      this.currentMode.deactivate();
    }

    // Activate new mode
    mode.activate();
    this.currentMode = mode;
    
    // Emit mode change event
    this.emitModeChange(mode);
    
    return true;
  }

  deactivateCurrentMode() {
    if (this.currentMode) {
      this.currentMode.deactivate();
      this.currentMode = null;
      this.emitModeChange(null);
    }
  }

  getCurrentMode(): BehavioralMode | null {
    return this.currentMode;
  }

  getAllModes(): BehavioralMode[] {
    return Array.from(this.modes.values());
  }

  getModeById(id: string): BehavioralMode | undefined {
    return this.modes.get(id);
  }

  private emitModeChange(mode: BehavioralMode | null) {
    // Emit custom event for mode change
    const event = new CustomEvent('superclaude-mode-change', {
      detail: { mode }
    });
    window.dispatchEvent(event);
  }

  // Get mode recommendations based on task
  recommendMode(taskDescription: string): string | null {
    const lowerTask = taskDescription.toLowerCase();
    
    if (lowerTask.includes('idea') || lowerTask.includes('brainstorm') || lowerTask.includes('creative')) {
      return 'brainstorming';
    }
    if (lowerTask.includes('coordinate') || lowerTask.includes('multiple') || lowerTask.includes('agents')) {
      return 'orchestration';
    }
    if (lowerTask.includes('quick') || lowerTask.includes('brief') || lowerTask.includes('summary')) {
      return 'token-efficiency';
    }
    if (lowerTask.includes('plan') || lowerTask.includes('task') || lowerTask.includes('organize')) {
      return 'task-management';
    }
    if (lowerTask.includes('why') || lowerTask.includes('reason') || lowerTask.includes('explain')) {
      return 'introspection';
    }
    
    return null;
  }
}

export const modeManager = new BehavioralModeManager();