// SuperClaude Command Registry - All 21 Commands
export interface Command {
  trigger: string;
  name: string;
  description: string;
  category: 'analysis' | 'development' | 'workflow' | 'management' | 'documentation';
  handler: (args: any) => Promise<any>;
  aliases?: string[];
  examples?: string[];
}

export class CommandRegistry {
  private commands: Map<string, Command> = new Map();

  constructor() {
    this.registerAllCommands();
  }

  private registerAllCommands() {
    // Analysis Commands
    this.register({
      trigger: '/sc:analyze',
      name: 'Analyze',
      description: 'Deep analysis of code, architecture, or problems',
      category: 'analysis',
      handler: async (args) => ({ type: 'analyze', args }),
      aliases: ['/sc:a'],
      examples: ['/sc:analyze performance issues', '/sc:analyze architecture']
    });

    this.register({
      trigger: '/sc:explain',
      name: 'Explain',
      description: 'Detailed explanation of concepts or code',
      category: 'documentation',
      handler: async (args) => ({ type: 'explain', args }),
      aliases: ['/sc:e'],
      examples: ['/sc:explain this algorithm', '/sc:explain design pattern']
    });

    this.register({
      trigger: '/sc:troubleshoot',
      name: 'Troubleshoot',
      description: 'Systematic debugging and problem-solving',
      category: 'analysis',
      handler: async (args) => ({ type: 'troubleshoot', args }),
      aliases: ['/sc:debug', '/sc:fix'],
      examples: ['/sc:troubleshoot memory leak', '/sc:troubleshoot API error']
    });

    // Development Commands
    this.register({
      trigger: '/sc:implement',
      name: 'Implement',
      description: 'Implementation of features or solutions',
      category: 'development',
      handler: async (args) => ({ type: 'implement', args }),
      aliases: ['/sc:impl', '/sc:code'],
      examples: ['/sc:implement user authentication', '/sc:implement cache layer']
    });

    this.register({
      trigger: '/sc:build',
      name: 'Build',
      description: 'Build systems, pipelines, or components',
      category: 'development',
      handler: async (args) => ({ type: 'build', args }),
      aliases: ['/sc:b'],
      examples: ['/sc:build CI/CD pipeline', '/sc:build microservice']
    });

    this.register({
      trigger: '/sc:test',
      name: 'Test',
      description: 'Create comprehensive test suites',
      category: 'development',
      handler: async (args) => ({ type: 'test', args }),
      aliases: ['/sc:t'],
      examples: ['/sc:test authentication flow', '/sc:test API endpoints']
    });

    this.register({
      trigger: '/sc:improve',
      name: 'Improve',
      description: 'Optimize and enhance existing code',
      category: 'development',
      handler: async (args) => ({ type: 'improve', args }),
      aliases: ['/sc:optimize', '/sc:enhance'],
      examples: ['/sc:improve performance', '/sc:improve readability']
    });

    this.register({
      trigger: '/sc:cleanup',
      name: 'Cleanup',
      description: 'Code cleanup and refactoring',
      category: 'development',
      handler: async (args) => ({ type: 'cleanup', args }),
      aliases: ['/sc:refactor', '/sc:clean'],
      examples: ['/sc:cleanup technical debt', '/sc:cleanup unused code']
    });

    // Workflow Commands
    this.register({
      trigger: '/sc:workflow',
      name: 'Workflow',
      description: 'Design and optimize workflows',
      category: 'workflow',
      handler: async (args) => ({ type: 'workflow', args }),
      aliases: ['/sc:wf', '/sc:flow'],
      examples: ['/sc:workflow deployment process', '/sc:workflow data pipeline']
    });

    this.register({
      trigger: '/sc:design',
      name: 'Design',
      description: 'System and architecture design',
      category: 'workflow',
      handler: async (args) => ({ type: 'design', args }),
      aliases: ['/sc:d', '/sc:architect'],
      examples: ['/sc:design microservices', '/sc:design database schema']
    });

    this.register({
      trigger: '/sc:brainstorm',
      name: 'Brainstorm',
      description: 'Creative ideation and solution exploration',
      category: 'workflow',
      handler: async (args) => ({ type: 'brainstorm', args }),
      aliases: ['/sc:idea', '/sc:explore'],
      examples: ['/sc:brainstorm feature ideas', '/sc:brainstorm solutions']
    });

    this.register({
      trigger: '/sc:estimate',
      name: 'Estimate',
      description: 'Time and resource estimation',
      category: 'management',
      handler: async (args) => ({ type: 'estimate', args }),
      aliases: ['/sc:est', '/sc:time'],
      examples: ['/sc:estimate project timeline', '/sc:estimate resource needs']
    });

    // Management Commands
    this.register({
      trigger: '/sc:task',
      name: 'Task',
      description: 'Task breakdown and management',
      category: 'management',
      handler: async (args) => ({ type: 'task', args }),
      aliases: ['/sc:todo', '/sc:plan'],
      examples: ['/sc:task create sprint plan', '/sc:task prioritize backlog']
    });

    this.register({
      trigger: '/sc:spawn',
      name: 'Spawn',
      description: 'Spawn specialized agents for tasks',
      category: 'management',
      handler: async (args) => ({ type: 'spawn', args }),
      aliases: ['/sc:agent', '/sc:invoke'],
      examples: ['/sc:spawn backend-architect', '/sc:spawn security-engineer']
    });

    this.register({
      trigger: '/sc:select-tool',
      name: 'Select Tool',
      description: 'Choose optimal tools and technologies',
      category: 'management',
      handler: async (args) => ({ type: 'select-tool', args }),
      aliases: ['/sc:tool', '/sc:tech'],
      examples: ['/sc:select-tool database', '/sc:select-tool framework']
    });

    // Documentation Commands
    this.register({
      trigger: '/sc:document',
      name: 'Document',
      description: 'Create comprehensive documentation',
      category: 'documentation',
      handler: async (args) => ({ type: 'document', args }),
      aliases: ['/sc:doc', '/sc:docs'],
      examples: ['/sc:document API endpoints', '/sc:document architecture']
    });

    this.register({
      trigger: '/sc:reflect',
      name: 'Reflect',
      description: 'Retrospective analysis and insights',
      category: 'documentation',
      handler: async (args) => ({ type: 'reflect', args }),
      aliases: ['/sc:retro', '/sc:review'],
      examples: ['/sc:reflect on sprint', '/sc:reflect on architecture decisions']
    });

    // Session Management Commands
    this.register({
      trigger: '/sc:save',
      name: 'Save',
      description: 'Save current context and state',
      category: 'management',
      handler: async (args) => ({ type: 'save', args }),
      aliases: ['/sc:s', '/sc:checkpoint'],
      examples: ['/sc:save progress', '/sc:save checkpoint']
    });

    this.register({
      trigger: '/sc:load',
      name: 'Load',
      description: 'Load saved context or configuration',
      category: 'management',
      handler: async (args) => ({ type: 'load', args }),
      aliases: ['/sc:l', '/sc:restore'],
      examples: ['/sc:load previous session', '/sc:load configuration']
    });

    this.register({
      trigger: '/sc:index',
      name: 'Index',
      description: 'Index and organize project knowledge',
      category: 'management',
      handler: async (args) => ({ type: 'index', args }),
      aliases: ['/sc:idx', '/sc:catalog'],
      examples: ['/sc:index codebase', '/sc:index documentation']
    });

    // Git Integration
    this.register({
      trigger: '/sc:git',
      name: 'Git',
      description: 'Git operations and workflow',
      category: 'workflow',
      handler: async (args) => ({ type: 'git', args }),
      aliases: ['/sc:g', '/sc:version'],
      examples: ['/sc:git commit changes', '/sc:git create branch']
    });
  }

  private register(command: Command) {
    this.commands.set(command.trigger, command);
    // Register aliases
    command.aliases?.forEach(alias => {
      this.commands.set(alias, command);
    });
  }

  getCommand(trigger: string): Command | undefined {
    return this.commands.get(trigger);
  }

  getAllCommands(): Command[] {
    // Return unique commands (not aliases)
    const uniqueCommands = new Map<string, Command>();
    this.commands.forEach(cmd => {
      if (!uniqueCommands.has(cmd.name)) {
        uniqueCommands.set(cmd.name, cmd);
      }
    });
    return Array.from(uniqueCommands.values());
  }

  getCommandsByCategory(category: Command['category']): Command[] {
    return this.getAllCommands().filter(cmd => cmd.category === category);
  }

  parseCommand(input: string): { command: Command | null; args: string } {
    const parts = input.split(/\s+/);
    const trigger = parts[0];
    const args = parts.slice(1).join(' ');
    
    const command = this.getCommand(trigger);
    return { command: command || null, args };
  }

  getSuggestions(input: string): Command[] {
    const lowercaseInput = input.toLowerCase();
    return this.getAllCommands().filter(cmd => 
      cmd.trigger.toLowerCase().includes(lowercaseInput) ||
      cmd.name.toLowerCase().includes(lowercaseInput) ||
      cmd.aliases?.some(alias => alias.toLowerCase().includes(lowercaseInput))
    );
  }
}

export const commandRegistry = new CommandRegistry();