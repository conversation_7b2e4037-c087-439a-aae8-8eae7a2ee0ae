import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  Brain, 
  Command, 
  Server, 
  Sparkles, 
  Users,
  Settings,
  ChevronRight,
  ChevronDown,
  Play,
  Pause,
  RotateCw
} from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { ScrollArea } from '@/components/ui/scroll-area';
import { commandRegistry } from '../commands/CommandRegistry';
import { modeManager } from '../modes/BehavioralModes';
import { mcpServerManager } from '../mcp-servers/MCPConfigurations';
import { cn } from '@/lib/utils';

interface SuperClaudePanelProps {
  sessionId: string;
  isExpanded?: boolean;
  onToggle?: () => void;
  onCommandExecute?: (command: any) => void;
  className?: string;
}

export const SuperClaudePanel: React.FC<SuperClaudePanelProps> = ({
  sessionId,
  isExpanded = false,
  onToggle,
  onCommandExecute,
  className
}) => {
  const [activeTab, setActiveTab] = useState('commands');
  const [selectedMode, setSelectedMode] = useState<string | null>(null);
  const [activeServers, setActiveServers] = useState<string[]>([]);
  const [commandHistory, setCommandHistory] = useState<any[]>([]);
  const [isExecuting, setIsExecuting] = useState(false);

  // Get all commands grouped by category
  const commandsByCategory = {
    analysis: commandRegistry.getCommandsByCategory('analysis'),
    development: commandRegistry.getCommandsByCategory('development'),
    workflow: commandRegistry.getCommandsByCategory('workflow'),
    management: commandRegistry.getCommandsByCategory('management'),
    documentation: commandRegistry.getCommandsByCategory('documentation')
  };

  // Get all behavioral modes
  const allModes = modeManager.getAllModes();

  // Get all MCP servers
  const allServers = mcpServerManager.getAllServerConfigs();

  const handleCommandClick = async (command: any) => {
    setIsExecuting(true);
    
    // Add to history
    setCommandHistory(prev => [{
      command: command.name,
      timestamp: Date.now(),
      status: 'executing'
    }, ...prev.slice(0, 9)]);

    // Execute command
    if (onCommandExecute) {
      await onCommandExecute(command);
    }

    // Update history status
    setCommandHistory(prev => {
      const updated = [...prev];
      if (updated[0]) {
        updated[0].status = 'completed';
      }
      return updated;
    });

    setIsExecuting(false);
  };

  const handleModeChange = (modeId: string) => {
    if (selectedMode === modeId) {
      modeManager.deactivateCurrentMode();
      setSelectedMode(null);
    } else {
      modeManager.activateMode(modeId);
      setSelectedMode(modeId);
    }
  };

  const handleServerToggle = async (serverId: string) => {
    if (activeServers.includes(serverId)) {
      await mcpServerManager.disconnectServer(serverId);
      setActiveServers(prev => prev.filter(id => id !== serverId));
    } else {
      const connected = await mcpServerManager.connectServer(serverId);
      if (connected) {
        setActiveServers(prev => [...prev, serverId]);
      }
    }
  };

  return (
    <div className={cn(
      "flex flex-col h-full bg-background/95 backdrop-blur-md border-l",
      className
    )}>
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b">
        <div className="flex items-center gap-2">
          <Brain className="h-5 w-5 text-purple-500" />
          <span className="font-semibold">SuperClaude</span>
          {selectedMode && (
            <Badge variant="secondary" className="ml-2">
              {modeManager.getModeById(selectedMode)?.name}
            </Badge>
          )}
        </div>
        <Button
          variant="ghost"
          size="icon"
          onClick={onToggle}
        >
          {isExpanded ? <ChevronRight /> : <ChevronDown />}
        </Button>
      </div>

      {/* Main Content */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="flex-1 flex flex-col">
        <TabsList className="grid w-full grid-cols-4 p-1 mx-4 mt-2">
          <TabsTrigger value="commands">
            <Command className="h-4 w-4 mr-1" />
            Commands
          </TabsTrigger>
          <TabsTrigger value="agents">
            <Users className="h-4 w-4 mr-1" />
            Agents
          </TabsTrigger>
          <TabsTrigger value="modes">
            <Sparkles className="h-4 w-4 mr-1" />
            Modes
          </TabsTrigger>
          <TabsTrigger value="servers">
            <Server className="h-4 w-4 mr-1" />
            MCP
          </TabsTrigger>
        </TabsList>

        <ScrollArea className="flex-1">
          {/* Commands Tab */}
          <TabsContent value="commands" className="p-4 space-y-4">
            {Object.entries(commandsByCategory).map(([category, commands]) => (
              <div key={category} className="space-y-2">
                <h3 className="text-sm font-medium capitalize text-muted-foreground">
                  {category}
                </h3>
                <div className="grid grid-cols-2 gap-2">
                  {commands.map(cmd => (
                    <Button
                      key={cmd.trigger}
                      variant="outline"
                      size="sm"
                      onClick={() => handleCommandClick(cmd)}
                      disabled={isExecuting}
                      className="justify-start"
                    >
                      <span className="truncate">{cmd.name}</span>
                    </Button>
                  ))}
                </div>
              </div>
            ))}
          </TabsContent>

          {/* Agents Tab */}
          <TabsContent value="agents" className="p-4 space-y-2">
            <div className="grid gap-2">
              {[
                'Backend Architect',
                'Frontend Architect',
                'Security Engineer',
                'Quality Engineer',
                'Python Expert',
                'System Architect',
                'Refactoring Expert',
                'Learning Guide',
                'Technical Writer',
                'Socratic Mentor',
                'Root Cause Analyst',
                'Performance Engineer',
                'Requirements Analyst',
                'DevOps Architect'
              ].map(agent => (
                <Button
                  key={agent}
                  variant="outline"
                  size="sm"
                  className="justify-between"
                  onClick={() => handleCommandClick({ 
                    name: `Spawn ${agent}`,
                    trigger: `/sc:spawn ${agent.toLowerCase().replace(' ', '-')}`
                  })}
                >
                  <span>{agent}</span>
                  <Play className="h-3 w-3" />
                </Button>
              ))}
            </div>
          </TabsContent>

          {/* Modes Tab */}
          <TabsContent value="modes" className="p-4 space-y-3">
            {allModes.map(mode => (
              <div
                key={mode.id}
                className={cn(
                  "p-3 rounded-lg border cursor-pointer transition-colors",
                  selectedMode === mode.id
                    ? "border-purple-500 bg-purple-500/10"
                    : "border-border hover:border-muted-foreground"
                )}
                onClick={() => handleModeChange(mode.id)}
              >
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <span className="text-2xl">{mode.icon}</span>
                    <div>
                      <h4 className="font-medium text-sm">{mode.name}</h4>
                      <p className="text-xs text-muted-foreground">
                        {mode.description}
                      </p>
                    </div>
                  </div>
                  {selectedMode === mode.id && (
                    <Badge variant="default">Active</Badge>
                  )}
                </div>
              </div>
            ))}
          </TabsContent>

          {/* MCP Servers Tab */}
          <TabsContent value="servers" className="p-4 space-y-3">
            {allServers.map(server => (
              <div
                key={server.id}
                className="p-3 rounded-lg border"
              >
                <div className="flex items-center justify-between mb-2">
                  <div>
                    <h4 className="font-medium text-sm">{server.name}</h4>
                    <p className="text-xs text-muted-foreground">
                      {server.description}
                    </p>
                  </div>
                  <Button
                    variant={activeServers.includes(server.id) ? "default" : "outline"}
                    size="sm"
                    onClick={() => handleServerToggle(server.id)}
                  >
                    {activeServers.includes(server.id) ? (
                      <>
                        <Pause className="h-3 w-3 mr-1" />
                        Connected
                      </>
                    ) : (
                      <>
                        <Play className="h-3 w-3 mr-1" />
                        Connect
                      </>
                    )}
                  </Button>
                </div>
                <div className="flex flex-wrap gap-1">
                  {server.capabilities.map(cap => (
                    <Badge key={cap} variant="secondary" className="text-xs">
                      {cap}
                    </Badge>
                  ))}
                </div>
              </div>
            ))}
          </TabsContent>
        </ScrollArea>
      </Tabs>

      {/* Command History Footer */}
      {commandHistory.length > 0 && (
        <div className="border-t p-3">
          <div className="flex items-center justify-between mb-2">
            <span className="text-xs font-medium text-muted-foreground">Recent Commands</span>
            <Button
              variant="ghost"
              size="icon"
              className="h-6 w-6"
              onClick={() => setCommandHistory([])}
            >
              <RotateCw className="h-3 w-3" />
            </Button>
          </div>
          <div className="space-y-1">
            {commandHistory.slice(0, 3).map((item, idx) => (
              <div
                key={idx}
                className="flex items-center justify-between text-xs"
              >
                <span className="text-muted-foreground">{item.command}</span>
                <Badge
                  variant={item.status === 'completed' ? 'default' : 'secondary'}
                  className="text-xs"
                >
                  {item.status}
                </Badge>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};