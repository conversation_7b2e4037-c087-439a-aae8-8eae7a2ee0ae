import React, { useState, useRef, useEffect } from "react";
import { motion, AnimatePresence } from "framer-motion";
import {
  Send,
  Paperclip,
  Image,
  Code,
  Sparkles,
  Zap,
  Brain,
  ChevronDown,
  X,
  AlertCircle,
  FileText,
  Hash,
  Mic,
  Settings,
  Loader2,
  <PERSON><PERSON><PERSON>
} from "lucide-react";
import { cn } from "@/lib/utils";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import { Separator } from "@/components/ui/separator";
import { FilePicker } from "./FilePicker";
import { SlashCommandPicker } from "./SlashCommandPicker";
import { ImagePreview } from "../display/ImagePreview";
import { api, type FileEntry, type SlashCommand, type ModelUsageInfo } from "@/lib/api";
import { useSuper<PERSON>laude } from "@/components/sessions/superclaude/hooks/useSuperClaude";
import { buildCommandPrompt } from "@/components/sessions/superclaude/core/parser";
import { Bot, Users, Cpu, Shield, Database, LayoutDashboard } from "lucide-react";
import { commandRegistry } from "@/components/sessions/superclaude/integrated/commands/CommandRegistry";
import { modeManager } from "@/components/sessions/superclaude/integrated/modes/BehavioralModes";
import { mcpServerManager } from "@/components/sessions/superclaude/integrated/mcp-servers/MCPConfigurations";
import { getCurrentWebviewWindow } from "@tauri-apps/api/webviewWindow";

interface SessionInputProps {
  onSend: (prompt: string, model: "claude-sonnet-4-20250514" | "claude-opus-4-1-20250805") => void;
  isLoading?: boolean;
  disabled?: boolean;
  defaultModel?: "claude-sonnet-4-20250514" | "claude-opus-4-1-20250805";
  projectPath?: string;
  className?: string;
  onCancel?: () => void;
  sessionId?: string;
  projectId?: string;
  onContinue?: () => void;
  isFirstPrompt?: boolean;
}

export interface SessionInputRef {
  addImage: (imagePath: string) => void;
  focus: () => void;
}

type ThinkingMode = "auto" | "think" | "think_hard" | "think_harder" | "ultrathink";

type ThinkingModeConfig = {
  id: ThinkingMode;
  name: string;
  description: string;
  icon: React.ReactNode;
  color: string;
  phrase?: string;
};

const THINKING_MODES: ThinkingModeConfig[] = [
  {
    id: "auto",
    name: "Auto",
    description: "Let Claude decide",
    icon: <Sparkles className="h-3.5 w-3.5" />,
    color: "text-muted-foreground"
  },
  {
    id: "think",
    name: "Think",
    description: "Basic reasoning",
    icon: <Brain className="h-3.5 w-3.5" />,
    color: "text-blue-500",
    phrase: "think"
  },
  {
    id: "think_hard",
    name: "Deep Think",
    description: "Complex analysis",
    icon: <Brain className="h-3.5 w-3.5" />,
    color: "text-purple-500",
    phrase: "think hard"
  },
  {
    id: "think_harder",
    name: "Advanced Think",
    description: "Multi-step reasoning",
    icon: <Brain className="h-3.5 w-3.5" />,
    color: "text-orange-500",
    phrase: "think harder"
  },
  {
    id: "ultrathink",
    name: "Ultra Think",
    description: "Maximum analysis",
    icon: <Zap className="h-3.5 w-3.5" />,
    color: "text-red-500",
    phrase: "ultrathink"
  }
];

const MODELS = [
  {
    id: "claude-sonnet-4-20250514",
    name: "Claude Sonnet 4",
    shortName: "Sonnet 4",
    icon: <Sparkles className="h-3.5 w-3.5" />,
    description: "Fast & efficient",
    color: "text-blue-500"
  },
  {
    id: "claude-opus-4-1-20250805",
    name: "Claude Opus 4.1",
    shortName: "Opus 4.1",
    icon: <Zap className="h-3.5 w-3.5" />,
    description: "Most capable",
    color: "text-purple-500"
  }
];

export const SessionInput = React.forwardRef<SessionInputRef, SessionInputProps>(((props, ref) => {
  const {
    onSend,
    isLoading = false,
    disabled = false,
    defaultModel = "claude-sonnet-4-20250514",
    projectPath,
    className,
    onCancel,
    sessionId,
    projectId,
    onContinue,
    isFirstPrompt = false
  } = props;

  const [prompt, setPrompt] = useState("");
  const [selectedModel, setSelectedModel] = useState<"claude-sonnet-4-20250514" | "claude-opus-4-1-20250805">(defaultModel);
  const [selectedThinkingMode, setSelectedThinkingMode] = useState<ThinkingMode>("auto");
  
  // SuperClaude state
  const [showCommandPalette, setShowCommandPalette] = useState(false);
  const [selectedSuperClaudeMode, setSelectedSuperClaudeMode] = useState<string | null>(null);
  const [activeAgents, setActiveAgents] = useState<string[]>([]);
  const [activeMCPServers, setActiveMCPServers] = useState<string[]>([]);
  const [commandSearch, setCommandSearch] = useState("");
  const [showModelPicker, setShowModelPicker] = useState(false);
  const [showThinkingPicker, setShowThinkingPicker] = useState(false);
  const [showFilePicker, setShowFilePicker] = useState(false);
  const [showSlashCommands, setShowSlashCommands] = useState(false);
  const [embeddedImages, setEmbeddedImages] = useState<string[]>([]);
  const [dragActive, setDragActive] = useState(false);
  const [usageInfo, setUsageInfo] = useState<ModelUsageInfo | null>(null);
  const [isFocused, setIsFocused] = useState(false);
  const [showPersonaActivation, setShowPersonaActivation] = useState(false);
  const [commandHistory, setCommandHistory] = useState<string[]>([]);
  const [showModeSelector, setShowModeSelector] = useState(false);
  const [showAgentPanel, setShowAgentPanel] = useState(false);
  const [showEnhancedDashboard, setShowEnhancedDashboard] = useState(false);
  const [isExecutingCommand, setIsExecutingCommand] = useState(false);
  const [currentExecutingCommand, setCurrentExecutingCommand] = useState<any>(null);

  const textareaRef = useRef<HTMLTextAreaElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);

  // SuperClaude Store integration - Temporarily disabled (store removed from original plan)
  const currentMode = null;
  const activeAgents = [];
  const agents = [];
  const availableModes = [];
  const superClaudeProcessing = false;
  const setSuperClaudeMode = () => {};
  const activateAgent = () => {};
  const deactivateAgent = () => {};
  const addSuperClaudeCommand = () => {};

  // Toggle agent function
  const toggleAgent = (agentId: string) => {
    if (activeAgents.includes(agentId)) {
      deactivateAgent(agentId);
      // Also remove from personas
      removePersona(agentId);
    } else {
      activateAgent(agentId);
      // Also activate as persona for prompt enhancement
      const agent = agents.find(a => a.id === agentId);
      if (agent) {
        // Build a SuperClaude command to activate this agent
        const commandText = `/sc:spawn ${agent.name}`;
        executeSuperClaudeCommand(commandText).then(parsed => {
          if (parsed) {
            console.log('Agent activated via SuperClaude:', agent.name);
          }
        });
      }
    }
  };

  // SuperClaude Hook integration
  const {
    activePersonas,
    suggestions: superClaudeSuggestions,
    selectedSuggestionIndex: superClaudeSelectedIndex,
    showSuggestions: showSuperClaudeSuggestions,
    processInput: processSuperClaudeInput,
    executeCommand: executeSuperClaudeCommand,
    selectCurrentSuggestion: selectCurrentSuperClaudeSuggestion,
    navigateSuggestions: navigateSuperClaudeSuggestions,
    clearSuggestions: clearSuperClaudeSuggestions,
    enhancePrompt: enhanceSuperClaudePrompt,
    removePersona,
    mightBeCommand: mightBeSuperClaudeCommand
  } = useSuperClaude({
    sessionId,
    projectId,
    useBackend: !!(sessionId && projectId),
    onCommandExecute: (command) => {
      // Add to command history
      setCommandHistory(prev => [...prev.slice(-9), command.trigger]);
      
      // Visual feedback
      setShowPersonaActivation(true);
      setTimeout(() => setShowPersonaActivation(false), 2000);
      
      window.dispatchEvent(new CustomEvent('superclaude-command-executed', {
        detail: command
      }));
    },
    onPersonaActivate: (personas) => {
      if (personas.length > 0) {
        setShowPersonaActivation(true);
        setTimeout(() => setShowPersonaActivation(false), 2500);
      }
    }
  });

  // Expose methods via ref
  React.useImperativeHandle(
    ref,
    () => ({
      addImage: (imagePath: string) => {
        setPrompt(currentPrompt => {
          const mention = imagePath.includes(' ') ? `@"${imagePath}"` : `@${imagePath}`;
          return currentPrompt + (currentPrompt.endsWith(' ') || currentPrompt === '' ? '' : ' ') + mention + ' ';
        });
      },
      focus: () => {
        textareaRef.current?.focus();
      }
    }),
    []
  );

  // Fetch usage info
  useEffect(() => {
    const fetchUsageInfo = async () => {
      try {
        const info = await api.getModelUsageInfo();
        setUsageInfo(info);
        if (!defaultModel) {
          const modelMap: Record<string, "claude-sonnet-4-20250514" | "claude-opus-4-1-20250805"> = {
            "sonnet": "claude-sonnet-4-20250514",
            "opus-4.1": "claude-opus-4-1-20250805"
          };
          setSelectedModel(modelMap[info.current_model] || "claude-sonnet-4-20250514");
        }
      } catch (error) {
      }
    };

    fetchUsageInfo();
    const interval = setInterval(fetchUsageInfo, 30000);
    return () => clearInterval(interval);
  }, [defaultModel]);

  // Auto-resize textarea
  useEffect(() => {
    if (textareaRef.current) {
      textareaRef.current.style.height = 'auto';
      const newHeight = Math.min(textareaRef.current.scrollHeight, 200);
      textareaRef.current.style.height = `${newHeight}px`;
    }
  }, [prompt]);

  const handleSend = () => {
    if (!prompt.trim() || disabled) return;

    let finalPrompt = prompt;
    
    // Add thinking mode phrase if selected
    const thinkingMode = THINKING_MODES.find(m => m.id === selectedThinkingMode);
    if (thinkingMode?.phrase) {
      finalPrompt = `${thinkingMode.phrase} ${finalPrompt}`;
    }

    // Add SuperClaude enhancements from personas
    if (activePersonas.length > 0) {
      finalPrompt = enhanceSuperClaudePrompt(finalPrompt);
    }
    
    // Add agent enhancements
    if (activeAgents.length > 0) {
      const agentNames = activeAgents
        .map(id => agents.find(a => a.id === id)?.name)
        .filter(Boolean);
      
      if (agentNames.length > 0) {
        finalPrompt = `[Active Agents: ${agentNames.join(', ')}]\n${finalPrompt}`;
      }
    }

    onSend(finalPrompt, selectedModel);
    setPrompt("");
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    // Handle Cmd/Ctrl+K for SuperClaude command palette
    if ((e.metaKey || e.ctrlKey) && e.key === 'k') {
      e.preventDefault();
      setShowCommandPalette(!showCommandPalette);
      return;
    }

    // Handle Escape to close command palette
    if (e.key === 'Escape' && showCommandPalette) {
      e.preventDefault();
      setShowCommandPalette(false);
      return;
    }

    // Handle SuperClaude suggestions navigation first (priority)
    if (showSuperClaudeSuggestions && superClaudeSuggestions.length > 0) {
      switch (e.key) {
        case 'ArrowDown':
          e.preventDefault();
          navigateSuperClaudeSuggestions('down');
          return;
        case 'ArrowUp':
          e.preventDefault();
          navigateSuperClaudeSuggestions('up');
          return;
        case 'Tab':
        case 'Enter':
          if (superClaudeSelectedIndex >= 0) {
            e.preventDefault();
            const selected = superClaudeSuggestions[superClaudeSelectedIndex];
            if (selected) {
              // Execute the command asynchronously with progress tracking
              setIsExecutingCommand(true);
              setCurrentExecutingCommand({
                command: selected.command.name,
                trigger: selected.command.trigger,
                personas: selected.command.personas || []
              });
              
              executeSuperClaudeCommand(selected.command.trigger).then(parsed => {
                if (parsed) {
                  // Build the command prompt with personas and send it
                  const enhancedPrompt = buildCommandPrompt(parsed);
                  onSend(enhancedPrompt, selectedModel);
                  setPrompt('');
                }
                
                // Hide progress after a short delay
                setTimeout(() => {
                  setIsExecutingCommand(false);
                  setCurrentExecutingCommand(null);
                }, 2000);
              });
              clearSuperClaudeSuggestions();
            }
            return;
          }
          break;
        case 'Escape':
          e.preventDefault();
          clearSuperClaudeSuggestions();
          return;
      }
    }
    
    // Handle command history navigation with Ctrl/Cmd + Up/Down
    if ((e.metaKey || e.ctrlKey) && commandHistory && commandHistory.length > 0) {
      if (e.key === 'ArrowUp' && commandHistory.length > 0) {
        e.preventDefault();
        const lastCommand = commandHistory[commandHistory.length - 1];
        setPrompt(lastCommand + ' ');
        processSuperClaudeInput(lastCommand + ' ');
        return;
      }
    }
    
    // Regular enter to send
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSend();
    }
  };

  const handleTextChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const newValue = e.target.value;
    setPrompt(newValue);
    
    // Smart command detection
    const hasSuperClaudeCommand = newValue.includes('/sc:');
    const hasRegularCommand = newValue.startsWith('/') && !hasSuperClaudeCommand;
    
    if (hasSuperClaudeCommand) {
      processSuperClaudeInput(newValue);
      setShowSlashCommands(false);
    } else if (hasRegularCommand) {
      setShowSlashCommands(true);
      clearSuperClaudeSuggestions();
    } else {
      setShowSlashCommands(false);
      // Still check for personas in regular text
      if (mightBeSuperClaudeCommand && mightBeSuperClaudeCommand(newValue)) {
        processSuperClaudeInput(newValue);
      }
    }
  };

  const selectedModelData = MODELS.find(m => m.id === selectedModel) || MODELS[0];
  const selectedThinkingData = THINKING_MODES.find(m => m.id === selectedThinkingMode) || THINKING_MODES[0];
  
  // Calculate usage warning level
  const usageWarning = usageInfo && Number(usageInfo.usage_percentage) >= 80;
  const usageCritical = usageInfo && Number(usageInfo.usage_percentage) >= 95;

  return (
    <>
      {/* SuperClaude Command Suggestions */}
      {showSuperClaudeSuggestions && superClaudeSuggestions.length > 0 && (
        <div className="fixed bottom-20 left-1/2 -translate-x-1/2 z-50 w-full max-w-2xl px-4">
          {/* Simple inline command suggestions */}
          <div className="bg-background/95 backdrop-blur-md border rounded-lg shadow-lg p-2 max-h-60 overflow-y-auto">
            {superClaudeSuggestions.map((suggestion, index) => (
              <button
                key={index}
                className={`w-full text-left px-3 py-2 rounded hover:bg-accent transition-colors ${
                  index === superClaudeSelectedIndex ? 'bg-accent' : ''
                }`}
                onClick={() => {
                  // Show progress for dropdown selection too
                  setIsExecutingCommand(true);
                  setCurrentExecutingCommand({
                    command: suggestion.command.name,
                    trigger: suggestion.command.trigger,
                personas: suggestion.command.personas || []
              });
              
              executeSuperClaudeCommand(suggestion.command.trigger).then(parsed => {
                if (parsed) {
                  const enhancedPrompt = buildCommandPrompt(parsed);
                  onSend(enhancedPrompt, selectedModel);
                  setPrompt('');
                }
                
                // Hide progress after a short delay
                setTimeout(() => {
                  setIsExecutingCommand(false);
                  setCurrentExecutingCommand(null);
                }, 2000);
              });
              clearSuperClaudeSuggestions();
            }}
          >
            <div className="flex items-center gap-2">
              <span className="text-sm font-medium">{suggestion.command.trigger}</span>
              <span className="text-xs text-muted-foreground">{suggestion.command.name}</span>
            </div>
            {suggestion.command.description && (
              <p className="text-xs text-muted-foreground mt-1">{suggestion.command.description}</p>
            )}
          </button>
        ))}
          </div>
        </div>
      )}

      {/* SuperClaude Command Palette */}
      {showCommandPalette && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          exit={{ opacity: 0, y: 20 }}
          className="fixed bottom-24 left-1/2 -translate-x-1/2 z-50 w-full max-w-3xl px-4"
        >
          <div className="bg-background/98 backdrop-blur-xl border rounded-xl shadow-2xl">
            {/* Command Palette Header */}
            <div className="flex items-center justify-between p-4 border-b">
              <div className="flex items-center gap-2">
                <Brain className="h-5 w-5 text-purple-500" />
                <span className="font-semibold">SuperClaude Commands</span>
                {selectedSuperClaudeMode && (
                  <Badge variant="secondary" className="ml-2">
                    {modeManager.getModeById(selectedSuperClaudeMode)?.name}
                  </Badge>
                )}
              </div>
              <Button
                variant="ghost"
                size="icon"
                onClick={() => setShowCommandPalette(false)}
                className="h-8 w-8"
              >
                <X className="h-4 w-4" />
              </Button>
            </div>

            {/* Command Search */}
            <div className="p-2 border-b">
              <input
                type="text"
                placeholder="Search commands..."
                value={commandSearch}
                onChange={(e) => setCommandSearch(e.target.value)}
                className="w-full px-3 py-2 bg-background/50 border rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-purple-500"
                autoFocus
              />
            </div>

            {/* Command Categories */}
            <div className="max-h-96 overflow-y-auto p-2">
              {Object.entries({
                analysis: commandRegistry.getCommandsByCategory('analysis'),
                development: commandRegistry.getCommandsByCategory('development'),
                workflow: commandRegistry.getCommandsByCategory('workflow'),
                management: commandRegistry.getCommandsByCategory('management'),
                documentation: commandRegistry.getCommandsByCategory('documentation')
              })
                .filter(([_, commands]) => 
                  commands.some(cmd => 
                    commandSearch === '' || 
                    cmd.name.toLowerCase().includes(commandSearch.toLowerCase()) ||
                    cmd.trigger.toLowerCase().includes(commandSearch.toLowerCase())
                  )
                )
                .map(([category, commands]) => (
                  <div key={category} className="mb-4">
                    <h3 className="text-xs font-semibold uppercase text-muted-foreground px-2 mb-2">
                      {category}
                    </h3>
                    <div className="grid grid-cols-2 gap-1">
                      {commands
                        .filter(cmd => 
                          commandSearch === '' || 
                          cmd.name.toLowerCase().includes(commandSearch.toLowerCase()) ||
                          cmd.trigger.toLowerCase().includes(commandSearch.toLowerCase())
                        )
                        .map(cmd => (
                          <button
                            key={cmd.trigger}
                            onClick={() => {
                              setPrompt(cmd.trigger + ' ');
                              setShowCommandPalette(false);
                              textareaRef.current?.focus();
                            }}
                            className="flex items-center gap-2 px-3 py-2 text-sm rounded-lg hover:bg-accent transition-colors text-left"
                          >
                            <Command className="h-3 w-3 text-muted-foreground" />
                            <div className="flex-1">
                              <div className="font-medium">{cmd.name}</div>
                              <div className="text-xs text-muted-foreground">{cmd.trigger}</div>
                            </div>
                          </button>
                        ))}
                    </div>
                  </div>
                ))}
            </div>

            {/* Mode & Agent Selector */}
            <div className="border-t p-3">
              <div className="flex items-center gap-2">
                {/* Mode Selector */}
                <select
                  value={selectedSuperClaudeMode || ''}
                  onChange={(e) => {
                    const modeId = e.target.value;
                    if (modeId) {
                      modeManager.activateMode(modeId);
                      setSelectedSuperClaudeMode(modeId);
                    } else {
                      modeManager.deactivateCurrentMode();
                      setSelectedSuperClaudeMode(null);
                    }
                  }}
                  className="flex-1 px-3 py-2 bg-background/50 border rounded-lg text-sm"
                >
                  <option value="">Default Mode</option>
                  {modeManager.getAllModes().map(mode => (
                    <option key={mode.id} value={mode.id}>
                      {mode.icon} {mode.name}
                    </option>
                  ))}
                </select>

                {/* Quick Agent Spawner */}
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => {
                    setPrompt('/sc:spawn ');
                    setShowCommandPalette(false);
                    textareaRef.current?.focus();
                  }}
                >
                  <Users className="h-4 w-4 mr-1" />
                  Spawn Agent
                </Button>
              </div>
            </div>
          </div>
        </motion.div>
      )}

      {/* Main Input Container */}
      <div
        ref={containerRef}
        className={cn(
          "fixed bottom-0 left-0 right-0 z-40",
          "bg-gradient-to-t from-background via-background to-background/95",
          "backdrop-blur-xl",
          dragActive && "ring-2 ring-primary/50 ring-offset-2",
          className
        )}
      >
        <div className="max-w-4xl mx-auto">
          {/* Usage Warning Bar */}
          <AnimatePresence>
            {usageInfo && Number(usageInfo.usage_percentage) > 70 && (
              <motion.div
                initial={{ height: 0, opacity: 0 }}
                animate={{ height: 'auto', opacity: 1 }}
                exit={{ height: 0, opacity: 0 }}
                className="overflow-hidden"
              >
                <div className={cn(
                  "px-4 py-2 flex items-center justify-between text-xs",
                  usageWarning && !usageCritical && "bg-yellow-500/10 text-yellow-600 dark:text-yellow-400",
                  usageCritical && "bg-red-500/10 text-red-600 dark:text-red-400"
                )}>
                  <div className="flex items-center gap-2">
                    <AlertCircle className="h-3 w-3" />
                    <span>
                      {Math.round(usageInfo.usage_percentage)}% of {Math.round(usageInfo.total_limit / 1000)}K tokens used
                    </span>
                  </div>
                  {usageCritical && (
                    <Badge variant="destructive" className="text-xs">
                      Limit approaching
                    </Badge>
                  )}
                </div>
              </motion.div>
            )}
          </AnimatePresence>

          {/* Enhanced Active Personas with Visual Feedback */}
          <AnimatePresence>
            {activePersonas.length > 0 && (
              <motion.div
                initial={{ height: 0, opacity: 0 }}
                animate={{ height: 'auto', opacity: 1 }}
                exit={{ height: 0, opacity: 0 }}
                transition={{ duration: 0.2 }}
                className="px-4 py-2 bg-gradient-to-r from-primary/5 to-purple-500/5"
              >
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <div className="flex items-center gap-2">
                      <Brain className="h-3.5 w-3.5 text-primary animate-pulse" />
                      <span className="text-xs font-medium bg-gradient-to-r from-primary to-purple-500 bg-clip-text text-transparent">
                        AI Personas:
                      </span>
                    </div>
                    {/* Simple inline persona display */}
                    {activePersonas && activePersonas.length > 0 && (
                      <div className="flex gap-1">
                        {activePersonas.map((persona, index) => (
                          <span key={index} className="text-xs px-2 py-1 bg-purple-500/10 text-purple-600 rounded-full">
                            {persona}
                          </span>
                        ))}
                      </div>
                    )}
                    {showPersonaActivation && (
                      <motion.span
                        initial={{ opacity: 0, scale: 0.8 }}
                        animate={{ opacity: 1, scale: 1 }}
                        exit={{ opacity: 0, scale: 0.8 }}
                        className="text-xs text-primary font-semibold flex items-center gap-1"
                      >
                        <Sparkles className="h-3 w-3 animate-spin" />
                        Enhanced!
                      </motion.span>
                    )}
                  </div>
                  <button
                    onClick={() => activePersonas.forEach(p => removePersona(p))}
                    className="text-xs text-muted-foreground hover:text-foreground transition-colors"
                  >
                    <X className="h-3 w-3" />
                  </button>
                </div>
              </motion.div>
            )}
          </AnimatePresence>

          {/* Image Previews */}
          <AnimatePresence>
            {embeddedImages.length > 0 && (
              <motion.div
                initial={{ height: 0, opacity: 0 }}
                animate={{ height: 'auto', opacity: 1 }}
                exit={{ height: 0, opacity: 0 }}
                className="px-4 pt-3 overflow-hidden"
              >
                <ImagePreview
                  images={embeddedImages}
                  onRemove={(img) => {
                    setEmbeddedImages(prev => prev.filter(i => i !== img));
                    // Remove from prompt
                    setPrompt(prev => prev.replace(`@"${img}"`, '').replace(`@${img}`, '').trim());
                  }}
                  className="pb-2"
                />
              </motion.div>
            )}
          </AnimatePresence>

          {/* Main Input Area */}
          <div className="p-4">
            <div className={cn(
              "relative flex items-end gap-2 rounded-2xl",
              "bg-muted/50 backdrop-blur-sm",
              "transition-all duration-200",
              isFocused && "shadow-lg shadow-primary/5"
            )}>
              {/* Left Action Buttons */}
              <div className="flex items-center gap-1 p-2 pb-3">
                <TooltipProvider>
                  <Separator orientation="vertical" className="h-6 mx-1" />

                  {/* SuperClaude Mode Selector */}
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Button
                        variant={currentMode !== 'orchestration' ? 'default' : 'ghost'}
                        size="icon"
                        className={cn(
                          "h-8 w-8 rounded-xl",
                          currentMode !== 'orchestration' && "bg-primary/10 text-primary hover:bg-primary/20"
                        )}
                        onClick={() => setShowModeSelector(!showModeSelector)}
                        disabled={disabled}
                      >
                        <Brain className="h-4 w-4" />
                      </Button>
                    </TooltipTrigger>
                    <TooltipContent>SuperClaude Mode: {currentMode}</TooltipContent>
                  </Tooltip>

                  {/* SuperClaude Agent Panel */}
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Button
                        variant={activeAgents.length > 0 ? 'default' : 'ghost'}
                        size="icon"
                        className={cn(
                          "h-8 w-8 rounded-xl relative",
                          activeAgents.length > 0 && "bg-green-500/10 text-green-600 hover:bg-green-500/20"
                        )}
                        onClick={() => setShowAgentPanel(!showAgentPanel)}
                        disabled={disabled}
                      >
                        <Bot className="h-4 w-4" />
                        {activeAgents.length > 0 && (
                          <span className="absolute -top-1 -right-1 h-3 w-3 bg-green-500 rounded-full text-[8px] text-white flex items-center justify-center">
                            {activeAgents.length}
                          </span>
                        )}
                      </Button>
                    </TooltipTrigger>
                    <TooltipContent>Active Agents: {activeAgents.length}</TooltipContent>
                  </Tooltip>

                  {/* Enhanced Dashboard Button */}
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Button
                        variant={showEnhancedDashboard ? 'default' : 'ghost'}
                        size="icon"
                        className={cn(
                          "h-8 w-8 rounded-xl",
                          showEnhancedDashboard && "bg-primary/10 text-primary hover:bg-primary/20"
                        )}
                        onClick={() => setShowEnhancedDashboard(!showEnhancedDashboard)}
                        disabled={disabled}
                      >
                        <LayoutDashboard className="h-4 w-4" />
                      </Button>
                    </TooltipTrigger>
                    <TooltipContent>Enhanced Dashboard</TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              </div>

              {/* Textarea */}
              <div className="flex-1 relative">
                {/* SuperClaude Active Indicator */}
                {prompt.startsWith('/sc:') && (
                  <div className="absolute top-1 left-2 flex items-center gap-1 px-2 py-0.5 bg-purple-500/10 text-purple-600 rounded-full text-xs">
                    <Brain className="h-3 w-3" />
                    <span>SuperClaude</span>
                  </div>
                )}
                <textarea
                  ref={textareaRef}
                  value={prompt}
                  onChange={handleTextChange}
                  onKeyDown={handleKeyDown}
                  onFocus={() => setIsFocused(true)}
                  onBlur={() => setIsFocused(false)}
                  placeholder={
                    isFirstPrompt 
                      ? "Start a new conversation..." 
                      : "Type your message..."
                  }
                  className={cn(
                    "w-full resize-none bg-transparent",
                    "text-sm leading-relaxed",
                    "placeholder:text-muted-foreground/60",
                    "focus:outline-none",
                    "py-3 pr-2",
                    "min-h-[44px] max-h-[200px]",
                    "scrollbar-thin scrollbar-thumb-border scrollbar-track-transparent"
                  )}
                  disabled={disabled}
                  rows={1}
                />
              </div>

              {/* Right Controls */}
              <div className="flex items-center gap-1 p-2 pb-3">
                {/* SuperClaude Button */}
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Button
                        variant={showCommandPalette ? "secondary" : "ghost"}
                        size="icon"
                        className="h-8 w-8 rounded-xl"
                        onClick={() => setShowCommandPalette(!showCommandPalette)}
                        disabled={disabled}
                      >
                        <Brain className={cn(
                          "h-4 w-4",
                          selectedSuperClaudeMode ? "text-purple-500" : "text-muted-foreground"
                        )} />
                      </Button>
                    </TooltipTrigger>
                    <TooltipContent>
                      <div className="text-xs">
                        <div>SuperClaude Commands (⌘K)</div>
                        {selectedSuperClaudeMode && (
                          <div className="text-purple-400 mt-1">Mode: {modeManager.getModeById(selectedSuperClaudeMode)?.name}</div>
                        )}
                      </div>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>

                <Separator orientation="vertical" className="h-6 mx-1" />

                {/* Model & Thinking Mode Selectors */}
                <div className="flex items-center gap-1 mr-1">
                  <TooltipProvider>
                    {/* Model Selector */}
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <Button
                          variant="ghost"
                          size="sm"
                          className={cn(
                            "h-7 px-2 gap-1.5 rounded-lg text-xs font-medium",
                            "hover:bg-background",
                            selectedModelData.color
                          )}
                          onClick={() => setShowModelPicker(!showModelPicker)}
                          disabled={disabled}
                        >
                          {selectedModelData.icon}
                          <span>{selectedModelData.shortName}</span>
                          <ChevronDown className="h-3 w-3 opacity-50" />
                        </Button>
                      </TooltipTrigger>
                      <TooltipContent>{selectedModelData.description}</TooltipContent>
                    </Tooltip>

                    {/* Thinking Mode Selector */}
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <Button
                          variant="ghost"
                          size="sm"
                          className={cn(
                            "h-7 px-2 gap-1.5 rounded-lg text-xs font-medium",
                            "hover:bg-background",
                            selectedThinkingData.color
                          )}
                          onClick={() => setShowThinkingPicker(!showThinkingPicker)}
                          disabled={disabled}
                        >
                          {selectedThinkingData.icon}
                          <ChevronDown className="h-3 w-3 opacity-50" />
                        </Button>
                      </TooltipTrigger>
                      <TooltipContent>
                        {selectedThinkingData.name}: {selectedThinkingData.description}
                      </TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                </div>

                <Separator orientation="vertical" className="h-6 mx-1" />

                {/* Send/Continue Button */}
                {isLoading ? (
                  <Button
                    variant="ghost"
                    size="icon"
                    className="h-8 w-8 rounded-xl"
                    onClick={onCancel}
                  >
                    <X className="h-4 w-4" />
                  </Button>
                ) : (
                  <>
                    {onContinue && !prompt.trim() && (
                      <TooltipProvider>
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <Button
                              variant="ghost"
                              size="icon"
                              className="h-8 w-8 rounded-xl hover:bg-primary hover:text-primary-foreground"
                              onClick={onContinue}
                              disabled={disabled}
                            >
                              <ArrowUp className="h-4 w-4" />
                            </Button>
                          </TooltipTrigger>
                          <TooltipContent>Continue</TooltipContent>
                        </Tooltip>
                      </TooltipProvider>
                    )}
                    
                    <Button
                      size="icon"
                      className={cn(
                        "h-8 w-8 rounded-xl",
                        "bg-primary hover:bg-primary/90",
                        "transition-all duration-200",
                        prompt.trim() && "animate-in fade-in-0 zoom-in-95"
                      )}
                      onClick={handleSend}
                      disabled={!prompt.trim() || disabled}
                    >
                      {isLoading ? (
                        <Loader2 className="h-4 w-4 animate-spin" />
                      ) : (
                        <Send className="h-4 w-4" />
                      )}
                    </Button>
                  </>
                )}
              </div>
            </div>

            {/* Helper Text */}
            <div className="mt-2 px-2 flex items-center justify-between">
              <div className="flex items-center gap-4 text-xs text-muted-foreground">
                <span>⌘ + Enter to send</span>
                <span>Shift + Enter for new line</span>
                {projectPath && (
                  <span className="flex items-center gap-1">
                    <FileText className="h-3 w-3" />
                    {projectPath.split('/').pop()}
                  </span>
                )}
              </div>
              
              {usageInfo && (
                <div className="flex items-center gap-2 text-xs text-muted-foreground">
                  <span>{Math.round((usageInfo as any).tokens_used || 0).toLocaleString()} tokens</span>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Model Picker Dropdown */}
        <AnimatePresence>
          {showModelPicker && (
            <motion.div
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: 10 }}
              className="absolute bottom-full mb-2 left-1/2 -translate-x-1/2 z-50"
            >
              <div className="bg-popover border border-border rounded-xl shadow-xl p-2 min-w-[240px]">
                {MODELS.map((model) => (
                  <button
                    key={model.id}
                    onClick={() => {
                      setSelectedModel(model.id as "claude-sonnet-4-20250514" | "claude-opus-4-1-20250805");
                      setShowModelPicker(false);
                    }}
                    className={cn(
                      "w-full flex items-center gap-3 px-3 py-2.5 rounded-lg",
                      "hover:bg-accent transition-colors",
                      "text-left",
                      selectedModel === model.id && "bg-accent"
                    )}
                  >
                    <div className={cn("flex-shrink-0", model.color)}>
                      {model.icon}
                    </div>
                    <div className="flex-1">
                      <div className="font-medium text-sm">{model.name}</div>
                      <div className="text-xs text-muted-foreground">
                        {model.description}
                      </div>
                    </div>
                    {selectedModel === model.id && (
                      <div className="h-2 w-2 rounded-full bg-primary" />
                    )}
                  </button>
                ))}
              </div>
            </motion.div>
          )}
        </AnimatePresence>

        {/* Thinking Mode Picker Dropdown */}
        <AnimatePresence>
          {showThinkingPicker && (
            <motion.div
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: 10 }}
              className="absolute bottom-full mb-2 left-1/2 -translate-x-1/2 z-50"
            >
              <div className="bg-popover border border-border rounded-xl shadow-xl p-2 min-w-[240px]">
                {THINKING_MODES.map((mode) => (
                  <button
                    key={mode.id}
                    onClick={() => {
                      setSelectedThinkingMode(mode.id);
                      setShowThinkingPicker(false);
                    }}
                    className={cn(
                      "w-full flex items-center gap-3 px-3 py-2.5 rounded-lg",
                      "hover:bg-accent transition-colors",
                      "text-left",
                      selectedThinkingMode === mode.id && "bg-accent"
                    )}
                  >
                    <div className={cn("flex-shrink-0", mode.color)}>
                      {mode.icon}
                    </div>
                    <div className="flex-1">
                      <div className="font-medium text-sm">{mode.name}</div>
                      <div className="text-xs text-muted-foreground">
                        {mode.description}
                      </div>
                    </div>
                    {selectedThinkingMode === mode.id && (
                      <div className="h-2 w-2 rounded-full bg-primary" />
                    )}
                  </button>
                ))}
              </div>
            </motion.div>
          )}
        </AnimatePresence>

        {/* SuperClaude Mode Selector Dropdown */}
        <AnimatePresence>
          {showModeSelector && (
            <motion.div
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: 10 }}
              className="absolute bottom-full mb-2 left-4 z-50"
            >
              <div className="bg-popover border border-border rounded-xl shadow-xl p-2 min-w-[280px]">
                <div className="text-xs font-medium text-muted-foreground px-2 py-1 mb-1">
                  SuperClaude Behavioral Mode
                </div>
                {availableModes.map((mode) => (
                  <button
                    key={mode}
                    onClick={() => {
                      setSuperClaudeMode(mode as BehavioralMode);
                      setShowModeSelector(false);
                    }}
                    className={cn(
                      "w-full flex items-start gap-3 px-3 py-2.5 rounded-lg",
                      "hover:bg-accent transition-colors text-left",
                      currentMode === mode && "bg-accent"
                    )}
                  >
                    <div className="flex-1">
                      <div className="font-medium text-sm">
                        {mode.split('-').map(word => word.charAt(0).toUpperCase() + word.slice(1)).join(' ')}
                      </div>
                      <div className="text-xs text-muted-foreground">
                        {mode === 'brainstorming' && 'Creative idea generation'}
                        {mode === 'orchestration' && 'Multi-agent coordination'}
                        {mode === 'token-efficiency' && 'Optimized token usage'}
                        {mode === 'task-management' && 'Organized task execution'}
                        {mode === 'introspection' && 'Self-reflective analysis'}
                      </div>
                    </div>
                    {currentMode === mode && (
                      <div className="h-2 w-2 rounded-full bg-primary mt-1" />
                    )}
                  </button>
                ))}
              </div>
            </motion.div>
          )}
        </AnimatePresence>

        {/* SuperClaude Agent Panel Dropdown */}
        <AnimatePresence>
          {showAgentPanel && (
            <motion.div
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: 10 }}
              className="absolute bottom-full mb-2 right-4 z-50"
            >
              <div className="bg-popover border border-border rounded-xl shadow-xl p-2 min-w-[320px] max-h-[400px] overflow-y-auto">
                <div className="text-xs font-medium text-muted-foreground px-2 py-1 mb-1">
                  Available Agents ({agents.length})
                </div>
                {agents.length === 0 ? (
                  <div className="px-3 py-4 text-sm text-muted-foreground text-center">
                    No agents available in {currentMode} mode
                  </div>
                ) : (
                  agents.map((agent) => (
                    <div
                      key={agent.id}
                      className={cn(
                        "flex items-start gap-3 px-3 py-2.5 rounded-lg",
                        "hover:bg-accent/50 transition-colors",
                        activeAgents.includes(agent.id) && "bg-accent"
                      )}
                    >
                      <input
                        type="checkbox"
                        checked={activeAgents.includes(agent.id)}
                        onChange={() => toggleAgent(agent.id)}
                        className="mt-0.5"
                      />
                      <div className="flex-1">
                        <div className="font-medium text-sm">{agent.name}</div>
                        <div className="text-xs text-muted-foreground">
                          {agent.description}
                        </div>
                        <div className="flex gap-1 mt-1">
                          {agent.capabilities?.slice(0, 3).map((cap, idx) => (
                            <Badge key={idx} variant="secondary" className="text-[10px] px-1 py-0">
                              {cap}
                            </Badge>
                          ))}
                        </div>
                      </div>
                    </div>
                  ))
                )}
              </div>
            </motion.div>
          )}
        </AnimatePresence>
      </div>

      {/* Enhanced Dashboard */}
      {showEnhancedDashboard && (
        <EnhancedDashboard
          sessionId={sessionId}
          projectId={projectId}
          position="right"
          onClose={() => setShowEnhancedDashboard(false)}
        />
      )}

      {/* Command Progress Indicator */}
      {isExecutingCommand && currentExecutingCommand && (
        <div className="fixed bottom-24 left-1/2 -translate-x-1/2 z-50 w-full max-w-md px-4">
          <CommandProgressIndicator
            commandName={currentExecutingCommand.command}
            commandTrigger={currentExecutingCommand.trigger}
            personas={currentExecutingCommand.personas}
            isExecuting={isExecutingCommand}
            onComplete={() => {
              setIsExecutingCommand(false);
              setCurrentExecutingCommand(null);
            }}
          />
        </div>
      )}
    </>
  );
}));

SessionInput.displayName = "SessionInput";